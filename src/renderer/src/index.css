@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%; /* slate-900 */

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --primary: 24 74% 58%; /* orange-500 */
    --primary-foreground: 0 0% 100%; /* white */

    --secondary: 33 100% 96%; /* orange-50 */
    --secondary-foreground: 194 100% 7%; /* orange-900 */

    --muted: 33 100% 96%; /* orange-50 */
    --muted-foreground: 33 5% 45%; /* neutral-600 */

    --accent: 33 100% 96%; /* orange-50 */
    --accent-foreground: 194 100% 7%; /* orange-900 */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 33 82% 89%; /* orange-200 */
    --input: 33 82% 89%; /* orange-200 */
    --ring: 24 74% 58%; /* orange-500 */

    --chart-1: 24 74% 58%; /* orange-500 */
    --chart-2: 45 93% 58%; /* amber-400 */
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --radius: 0.75rem;

    --sidebar-background: 33 100% 98%; /* orange-25 */
    --sidebar-foreground: 33 5.3% 26.1%;
    --sidebar-primary: 24 74% 58%; /* orange-500 */
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 33 100% 95%; /* orange-100 */
    --sidebar-accent-foreground: 194 100% 7%; /* orange-900 */
    --sidebar-border: 33 82% 89%; /* orange-200 */
    --sidebar-ring: 24 74% 58%; /* orange-500 */
  }

  .dark {
    --background: 222.2 47.4% 11.2%; /* slate-900 */
    --foreground: 210 40% 98%; /* slate-50 */

    --card: 222.2 47.4% 11.2%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 47.4% 11.2%;
    --popover-foreground: 210 40% 98%;

    --primary: 24 74% 58%; /* orange-500 */
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%; /* slate-800 */
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%; /* slate-400 */

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%; /* slate-300 */

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 24 74% 58%; /* orange-500 */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 24 74% 58%; /* orange-500 */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      'Inter',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      'Roboto',
      sans-serif;
  }
}

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

/* 初始化页面自定义动画 */
@layer components {
  /* 自定义呼吸动画 */
  @keyframes breathe {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.9;
    }
  }

  @keyframes gentle-pulse {
    0%,
    100% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.1);
    }
  }

  @keyframes soft-glow {
    0%,
    100% {
      box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
    }
    50% {
      box-shadow: 0 0 16px rgba(255, 255, 255, 0.5);
    }
  }

  /* 进行中状态的呼吸效果 */
  .animate-breathe {
    animation: breathe 2s ease-in-out infinite;
  }

  .animate-gentle-pulse {
    animation: gentle-pulse 1.5s ease-in-out infinite;
  }

  .animate-soft-glow {
    animation: soft-glow 2s ease-in-out infinite;
  }

  /* 悬停时的微妙缩放效果 */
  .group:hover .step-dot {
    transform: scale(1.1);
  }

  /* 渐变连接线的动画效果 */
  .progress-line {
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 1) 0%,
      rgba(255, 255, 255, 0.9) 50%,
      rgba(255, 255, 255, 0.7) 100%
    );
    transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 步骤文字的柔和过渡 */
  .step-text {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 状态指示点的脉冲效果 */
  .status-dot-pulse {
    animation: gentle-pulse 2s ease-in-out infinite;
  }

  /* 响应式优化 */
  @media (max-width: 640px) {
    .step-dot {
      width: 12px;
      height: 12px;
    }

    .step-text {
      font-size: 10px;
      line-height: 1.2;
    }
  }

  /* 增强的呼吸效果 */
  @keyframes deep-breathe {
    0%,
    100% {
      transform: scale(1) rotate(0deg);
      opacity: 1;
    }
    25% {
      transform: scale(1.02) rotate(0.5deg);
      opacity: 0.95;
    }
    50% {
      transform: scale(1.05) rotate(0deg);
      opacity: 0.9;
    }
    75% {
      transform: scale(1.02) rotate(-0.5deg);
      opacity: 0.95;
    }
  }

  /* 进行中状态的深度呼吸效果 */
  .animate-deep-breathe {
    animation: deep-breathe 3s ease-in-out infinite;
  }

  /* 光环扩散效果优化 */
  .ping-ring {
    animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
  }

  @keyframes ping {
    75%,
    100% {
      transform: scale(2);
      opacity: 0;
    }
  }

  /* 自定义滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }

  /* 诊断报告区域的滚动条 */
  .scrollbar-thumb-white\/10::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .hover\:scrollbar-thumb-white\/20::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .hover\:scrollbar-thumb-white\/30::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }
}
