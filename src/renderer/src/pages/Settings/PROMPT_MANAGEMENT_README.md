# 提示词管理模块

本文档介绍了新增的提示词管理功能，该功能允许用户在 Settings 页面中管理自定义提示词，用于生成个性化的分析报告。

## 功能概述

### 核心功能
- ✅ **增加提示词**：创建新的自定义提示词模板
- ✅ **删除提示词**：删除不需要的自定义提示词（内置提示词不可删除）
- ✅ **修改提示词**：编辑现有提示词的名称、描述、分类和内容
- ✅ **查询提示词**：通过搜索和分类筛选查找提示词
- ✅ **复制提示词**：复制内置或自定义提示词作为新模板
- ✅ **预览提示词**：查看提示词的完整内容和详细信息

### 提示词分类
- **情感分析**：分析聊天中的情感变化和情绪模式
- **人格分析**：深入了解性格特征和行为模式  
- **关系分析**：分析与特定联系人的互动模式
- **工作分析**：分析工作群聊的氛围和团队动态
- **自定义**：其他类型的自定义分析

## 技术实现

### 文件结构
```
src/renderer/src/pages/Settings/
├── types.ts                          # 新增提示词相关类型定义
├── constants.ts                      # 新增内置提示词和分类常量
├── hooks/
│   ├── useSettings.ts                # 扩展：添加提示词管理状态
│   ├── usePromptManagement.ts        # 新增：提示词管理逻辑
│   └── useToastNotifications.ts      # 扩展：添加提示词相关通知
├── components/
│   ├── PromptManagementTab.tsx       # 新增：提示词管理主页面
│   ├── PromptCard.tsx                # 新增：提示词卡片组件
│   ├── PromptPreviewDialog.tsx       # 新增：提示词预览对话框
│   └── PromptFormDialog.tsx          # 新增：添加/编辑提示词表单
└── index.tsx                         # 更新：集成提示词管理标签页
```

### 新增类型定义

```typescript
// 提示词模板接口
interface PromptTemplate {
  id: string
  name: string
  description: string
  content: string
  category: 'emotion' | 'personality' | 'relationship' | 'work' | 'custom'
  isBuiltIn: boolean
  createdAt: string
  updatedAt: string
}

// 新建提示词接口
interface NewPromptTemplate {
  name: string
  description: string
  content: string
  category: 'emotion' | 'personality' | 'relationship' | 'work' | 'custom'
}
```

### 内置提示词

系统预置了 4 个内置提示词模板：
1. **情感分析**：分析聊天记录中的情感变化和情绪模式
2. **人格分析**：基于聊天记录分析个人性格特征
3. **关系分析**：分析与联系人的互动模式和关系发展
4. **工作氛围分析**：分析工作群聊的团队动态和协作模式

## 用户界面

### 主要功能区域

1. **搜索和筛选区**
   - 搜索框：支持按名称和描述搜索
   - 分类筛选：按提示词分类筛选
   - 添加按钮：快速创建新提示词

2. **统计信息区**
   - 总提示词数量
   - 自定义提示词数量  
   - 内置提示词数量

3. **提示词列表区**
   - 卡片式展示每个提示词
   - 支持预览、复制、编辑、删除操作
   - 内置提示词只能预览和复制

4. **使用说明区**
   - 功能使用指南
   - 操作注意事项

### 交互设计

- **添加提示词**：点击"添加提示词"按钮打开表单对话框
- **编辑提示词**：点击提示词卡片上的编辑图标
- **预览提示词**：点击眼睛图标查看完整内容
- **复制提示词**：点击复制图标创建副本
- **删除提示词**：点击删除图标（仅限自定义提示词）

## 数据管理

### 状态管理
- 提示词数据存储在 `SettingsState.promptTemplates` 中
- 通过 `useSettings` hook 管理状态
- 支持增删改查操作的响应式更新

### 数据持久化
- 当前实现为内存存储
- 后续可扩展为本地存储或云端同步
- 内置提示词在每次初始化时自动加载

## 集成说明

### Settings 页面集成
- 新增"提示词管理"标签页
- 标签栏从 4 列扩展为 5 列
- 完整的 CRUD 功能集成

### 与报告生成的集成
- 提示词数据结构已准备就绪
- 可在 GenerateReport 页面中选择使用
- 支持自定义提示词和内置提示词

## 扩展性

### 未来功能扩展
1. **提示词导入/导出**：支持批量管理
2. **提示词模板市场**：分享和下载社区模板
3. **智能推荐**：基于使用历史推荐合适的提示词
4. **版本管理**：提示词的版本控制和回滚
5. **协作功能**：团队共享提示词模板

### 技术扩展
1. **数据持久化**：本地存储或云端同步
2. **搜索优化**：全文搜索和智能匹配
3. **性能优化**：虚拟滚动和懒加载
4. **国际化**：多语言支持

## 使用指南

### 创建自定义提示词
1. 点击"添加提示词"按钮
2. 填写提示词名称和描述
3. 选择合适的分类
4. 编写详细的提示词内容
5. 点击"添加提示词"保存

### 管理现有提示词
1. 使用搜索框快速查找
2. 通过分类筛选缩小范围
3. 点击相应图标进行操作
4. 内置提示词可复制后修改

### 最佳实践
1. **清晰命名**：使用描述性的提示词名称
2. **详细描述**：添加使用场景和目的说明
3. **结构化内容**：使用编号和分段组织提示词
4. **合理分类**：选择最匹配的分类便于管理
5. **定期维护**：及时更新和优化提示词内容

## 总结

提示词管理模块为 EchoSoul 应用增加了强大的自定义分析能力，用户可以：
- 创建个性化的分析模板
- 管理和组织提示词库
- 提高报告生成的灵活性和针对性
- 积累和复用优质的分析模板

该模块采用模块化设计，代码结构清晰，易于维护和扩展，为后续功能开发奠定了良好基础。
