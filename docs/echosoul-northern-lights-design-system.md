# EchoSoul × Northern Lights 设计系统

## 🎨 设计理念融合

EchoSoul 的全新设计系统采用了 **现代简约美学**与**高对比度配色**，追求清晰、专业、现代的视觉体验。

### 核心设计原则

1. **简约优雅** - 去除冗余，突出核心功能
2. **高对比度** - 确保优秀的可读性和可访问性
3. **现代专业** - 色彩传达专业、智能、现代的品牌个性
4. **无障碍设计** - 符合 WCAG 2.1 AA 标准

## 🌈 色彩系统

### 主色系 (Primary)

- **Light**: `#4f46e5` - 深邃靛蓝，象征专业与智慧
- **Dark**: `#818cf8` - 明亮靛蓝，深色模式下的优雅呈现

### 辅助色系 (Secondary)

- **Light**: `#14b8a6` - 青绿色，现代而清新
- **Dark**: `#2dd4bf` - 明亮青绿，深色模式下的活力色彩

### 强调色系 (Accent)

- **Light**: `#f59e0b` - 温暖橙黄，吸引注意力
- **Dark**: `#fcd34d` - 明亮黄色，深色模式下的醒目色彩

### 背景色系

- **Light**: `#f7f9f3` - 温暖浅绿背景，舒适护眼
- **Dark**: `#000000` - 纯黑背景，极致对比

### 数据可视化色彩

1. `#4f46e5` - 深邃靛蓝
2. `#14b8a6` - 青绿色
3. `#f59e0b` - 温暖橙黄
4. `#ec4899` - 活力粉红
5. `#22c55e` - 生机绿色

## 🔤 字体系统

### 字体家族

- **Sans-serif**: DM Sans - 现代、清晰、专业
- **Serif**: DM Sans - 统一字体家族，保持一致性
- **Monospace**: Space Mono - 等宽字体，适合代码和数据显示

### 字体层级

- **标题**: 24px - 页面主标题
- **副标题**: 20px - 区域标题
- **正文**: 16px - 主要内容
- **小字**: 14px - 辅助信息
- **标签**: 12px - 标签和说明

## 🎭 阴影系统

使用轻微的阴影，营造现代简约的层次感：

- **2xs**: `0px 0px 0px 0px hsl(0 0% 10.1961% / 0.03)` - 极轻微
- **sm**: `0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.05)` - 轻微
- **md**: `0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 2px 4px -1px hsl(0 0% 10.1961% / 0.05)` - 中等
- **lg**: `0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 4px 6px -1px hsl(0 0% 10.1961% / 0.05)` - 较强

## 🔄 圆角系统

- **Small**: `calc(1rem - 4px)` = 12px
- **Medium**: `calc(1rem - 2px)` = 14px
- **Large**: `1rem` = 16px
- **Extra Large**: `calc(1rem + 4px)` = 20px

## 📐 间距系统

基于 `0.25rem` (4px) 的倍数系统，确保视觉节奏的一致性。

## 🌙 深色模式

完整支持深色模式，采用纯黑背景和高对比度配色：

- **背景**: 纯黑 `#000000`
- **卡片**: 深灰 `#1a212b`
- **主色**: 明亮靛蓝 `#818cf8`
- **辅助色**: 明亮青绿 `#2dd4bf`
- **强调色**: 明亮黄色 `#fcd34d`

## 🎯 应用指南

### 卡片设计

- 使用 `var(--card)` 背景
- 应用 `var(--shadow-md)` 阴影
- 圆角使用 `var(--radius)` (16px)

### 按钮设计

- 主要按钮使用 `var(--primary)` 背景
- 次要按钮使用 `var(--secondary)` 背景
- 强调按钮使用 `var(--accent)` 背景

### 导航设计

- 侧边栏使用 `var(--sidebar)` 背景
- 活跃状态使用 `var(--sidebar-primary)`
- 悬停状态使用 `var(--sidebar-accent)`

### 色彩使用原则

1. **主色 (#4f46e5)**: 用于主要操作按钮、链接、重要状态指示
2. **辅助色 (#14b8a6)**: 用于次要操作、成功状态、进度指示
3. **强调色 (#f59e0b)**: 用于警告、重要提醒、特殊标记
4. **破坏性色彩 (#ef4444)**: 用于错误状态、删除操作、危险警告

---

_最后更新：2025-07-30_
_版本：EchoSoul × Northern Lights v2.0 - Modern Edition_
