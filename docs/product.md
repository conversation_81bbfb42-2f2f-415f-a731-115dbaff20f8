# EchoSoul MVP 版本 - 产品规格文档

## 产品定位

**"用AI把微信聊天变成个性化洞察"**

EchoSoul 是一个读取微信聊天记录，结合用户自定义 Prompt，秒生成专属分析报告的跨平台软件。

## MVP 核心价值

### 唯一功能：聊天记录分析报告

- **个性化报告生成**：用户指定时间范围、聊天对象获取聊天记录配合Prompt生成个性化报告

### 支撑功能（最小化）

- **报告管理**：查看历史报告，简单的列表和详情展示
- **环境配置**：连接 chatlog 服务，获取聊天数据
- **基础设置**：AI 服务配置，报告生成偏好设置

## 目标用户

**主要用户群体：自我探索型用户（25-35 岁）**

- 对自我认知有强烈需求
- 愿意尝试新的自我发现方式
- 重视隐私和数据安全
- 有一定的数字产品使用经验

## 功能设计

#### 报告生成

```yaml
功能描述:
  - 用户可选择任意时间范围（昨天、上周、上月、自定义）
  - 可指定特定聊天对象（某个朋友、某个群聊）
  - 选择用于分析的系统提示词(Prompt)

表单配置示例:
  场景1 - 联系人分析:
    时间范围: '最近一个月'
    分析对象: '张三'
    Prompt: '情感分析'

  场景2 - 群聊分析:
    时间范围: '最近两周'
    分析对象: '工作群'
    Prompt: '工作氛围'

  场景3 - 情商分析:
    时间范围: '最近一周'
    分析对象: '全部聊天'
    Prompt: '情商提升'

  场景4 - 思维陷阱分析:
    时间范围: '最近一个月'
    分析对象: ['张三', '李四', '王五']
    Prompt: '人格分析'
```

### 2. 报告管理（支撑功能）

#### 报告查看和管理

- 历史报告列表和搜索
- 报告详情查看
- 报告导出功能

#### 报告内容展示

```yaml
报告结构:
  标题: '2024年7月1日 聊天分析报告'

  概览卡片:
    - 消息总数: 45条
    - 主要联系人: 张三、李四、工作群
    - 分析结果: [一句话总结]

  详细分析: [根据Prompt生成的详情内容]
```

## 用户流程设计

```
第1步：环境设置
├── 检测chatlog服务状态
├── 安装/启动chatlog服务
├── 验证API连接

第2步：AI配置
├── 选择AI服务提供商
├── 输入API Key
├── 创建Prompt
├── 测试连接和模型可用性

第3步：生成报告
├── 设置查询条件&Prompt
├── 生成分析报告
└── 验证报告生成功能

第4步：查看报告
├── 展示分析结果
├── 解读报告内容

第5步：日常使用
├── 生成自定义报告
└── 管理和导出历史报告
```

## 界面设计原则

### 极简设计

- 每个界面只有一个主要操作
- 减少视觉装饰，专注功能实现
- 使用简单的文字说明，避免复杂图标

### 温暖的产品语言

- 用日常语言而非技术术语
- 积极正面的表达方式
- 体现陪伴和理解的产品调性

## 技术架构

### 技术选型原则

- **简单优先**：选择最简单可行的技术方案
- **本地优先**：95%功能本地处理
- **快速迭代**：支持快速开发和部署
- **成本控制**：最小化外部依赖和 API 费用

### 核心技术栈

- **桌面框架**：Electron（跨平台）
- **前端**：Vue + TypeScript
- **AI 处理**：LangChain.js + 多 LLM Provider 支持
- **数据处理**：基于 chatlog API + SQLite
- **界面**：tailwind css，精致高级且专注功能实现

### AI 服务架构

```yaml
LangChain集成:
  支持的LLM Provider:
    - OpenAI (GPT-3.5/GPT-4)
    - Anthropic (Claude)
    - Google (Gemini)
    - OpenRouter (多模型聚合)
    - DeepSeek (国产模型)

  用户配置:
    - 用户自带API Key
    - 可选择使用的模型
    - 本地安全存储API Key
    - 支持模型切换
```

### API Key 管理

```yaml
安全存储:
  - 使用系统keychain存储API Key
  - 本地加密，不上传到任何服务器
  - 支持多个Provider的Key管理

用户配置界面:
  - 简单的设置页面
  - 测试API Key有效性
  - 显示当前使用的模型和费用预估
  - 提供使用量统计
```

_最后更新：2025-07-30_
